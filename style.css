/*
Theme Name: LN Reader
Theme URI: http://your-domain.com/ln-reader
Author: Your Name
Author URI: http://your-domain.com
Description: A WordPress theme for light novel reading website
Version: 1.1
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ln-reader
*/

/* Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Fira+Sans:wght@400;500;700&display=swap');

/* Base Styles */
body {
    font-family: 'Fira Sans', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    background: #f8f9fa;
    color: #222;
}

a {
    color: #555;
    text-decoration: none;
    transition: color .1s linear;
}

a:hover {
    color: #0c70de;
    text-decoration: none;
}

/* Headings */
h1, .h1 {
    font-family: 'Fira Sans', sans-serif;
    font-size: 18px;
    font-weight: 500;
    line-height: 1.3;
}

h2, .h2 {
    font-family: 'Fira Sans', sans-serif;
    font-size: 16px;
    font-weight: 500;
}

h3, .h3 {
    font-family: 'Fira Sans', sans-serif;
    font-size: 14px;
    font-weight: 500;
}

h4, .h4, h5, .h5 {
    font-family: 'Fira Sans', sans-serif;
    font-size: 13px;
    font-weight: 500;
}

/* Header */
.navbar {
    background-color: #343a40;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Main Layout Improvements */
.site-content {
    padding: 2rem 0;
}

.container {
    max-width: 1500px !important;
    padding-right: 1rem;
    padding-left: 1rem;
    margin-right: auto;
    margin-left: auto;
}

/* Card Styling */
.card {
    border: none;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background: #fff;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Novel Grid */
.novel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.novel-card {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #fff;
    border-bottom: 1px solid #edf2f7;
}

.novel-card:last-child {
    border-bottom: none;
}

.novel-cover {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0;
}

.novel-cover img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: block;
}

.novel-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.novel-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    color: #2c3e50;
}

.novel-status {
    position: absolute;
    top: 8px;
    right: 8px;
}

.novel-info {
    padding: 0.75rem;
}

.novel-card .novel-title {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.novel-card .novel-title a {
    color: #333;
    text-decoration: none;
}

.novel-card .novel-title a:hover {
    color: #007bff;
}

.novel-meta {
    font-size: 13px;
    color: #8f95a3;
}

.novel-meta .author {
    display: block;
    margin-bottom: 0.25rem;
}

.novel-meta .genres {
    margin-top: 0.5rem;
}

.novel-meta .badge {
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.25rem;
}

/* Latest Chapter Releases */
.chapter-releases {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.chapter-releases .section-title {
    font-size: 1.25rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.chapter-release-item {
    display: flex;
    gap: 0.75rem;
    padding: 0.75rem;
    border-bottom: 1px solid #edf2f7;
}

.chapter-release-item:last-child {
    border-bottom: none;
}

.chapter-release-cover {
    width: 45px;
    height: 65px;
    flex-shrink: 0;
}

.chapter-release-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.chapter-release-info {
    flex: 1;
    min-width: 0;
    padding-top: 2px;
}

.chapter-release-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    text-decoration: none;
    margin-bottom: 0.25rem;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 800px;
}

.chapter-release-title:hover {
    color: #3498db;
}

.chapter-release-cover {
    width: 50px;
    height: 70px;
    flex-shrink: 0;
}

.chapter-release-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.chapter-release-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.chapter-release-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    color: #2c3e50;
    text-decoration: none;
}

.chapter-release-title:hover {
    color: #3498db;
    text-decoration: none;
}

/* Navigation Tabs */
.nav-tabs {
    border: none;
    margin-bottom: 1.5rem;
    gap: 0.5rem;
}

.nav-tabs .nav-link {
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    color: #6c757d;
    background: #e9ecef;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    color: #2c3e50;
    background: #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #fff;
    background: #3498db;
}

/* Popular Novels Section */
.popular-novels {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.popular-novels .section-title {
    font-size: 1.25rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.popular-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 6px;
    background: #fff;
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
    transition: transform 0.2s ease;
}

.popular-item:hover {
    transform: translateX(4px);
}

.popular-item-cover {
    width: 80px;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
}

.popular-item-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-item-chapter {
    margin-top: 0.25rem;
    width: 120%;
}

.popular-item-chapter .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popular-item-chapter a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 1rem;
    white-space: nowrap;
}

.popular-item-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin: 2px 0;
}

.chapter-time {
    font-size: 0.85rem;
    color: #718096;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 85px;
    text-align: right;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .novel-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }

    .site-content {
        padding: 1rem 0;
    }

    .chapter-releases,
    .popular-novels {
        padding: 1rem;
    }
}

/* Reading Container */
.reading-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
}

@media (min-width: 768px) {
    .reading-container {
        max-width: 900px;
        padding: 0;
    }
}

/* Chapter Content */
.chapter-content {
    font-size: 1.125rem;
    line-height: 1.8;
    padding: 1rem;
}

@media (max-width: 768px) {
    /* Typography */
    body {
        font-size: 14px;
    }

    h1, .h1 {
        font-size: 1.5rem;
    }

    h2, .h2 {
        font-size: 1.25rem;
    }

    /* Navigation */
    .navbar-brand {
        font-size: 1.25rem;
    }

    .navbar-nav {
        padding: 1rem 0;
    }

    /* Novel Cards */
    .novel-card {
        margin-bottom: 1rem;
    }

    .novel-card .card-body {
        padding: 0.75rem;
    }

    /* Chapter Navigation */
    .chapter-nav {
        padding: 0.75rem !important;
    }

    .chapter-nav .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Reading Experience */
    .chapter-content {
        font-size: 1rem;
        line-height: 1.6;
        padding: 0.75rem;
    }

    .chapter-title {
        font-size: 1.25rem;
    }

    /* Comments Section */
    .comments-section {
        padding: 0.75rem !important;
    }

    /* Social Share */
    .social-share {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    /* Novel Details */
    .novel-meta {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .novel-cover {
        max-width: 200px;
        margin: 0 auto 1rem;
    }

    /* Search and Filters */
    .search-box {
        width: 100%;
        margin-bottom: 1rem;
    }

    .filter-section {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-section select {
        width: 100%;
    }
}

/* Touch Improvements */
@media (hover: none) {
    .btn, 
    .nav-link,
    .chapter-link {
        padding: 0.75rem 1rem;
    }

    .novel-card {
        cursor: pointer;
    }

    select, 
    input[type="text"],
    input[type="search"] {
        font-size: 16px !important; /* Prevents zoom on iOS */
    }
}

/* Sidebar */
.sidebar .card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.sidebar .card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.sidebar .card-header h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.sidebar .card-body {
    padding: 1rem;
}

/* Popular Novels */
.popular-novel-item {
    align-items: center;
}

.popular-novel-item .novel-thumb {
    width: 50px;
    height: 70px;
    border-radius: 4px;
    overflow: hidden;
}

.popular-novel-item .novel-info h6 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Latest Chapters */
.chapter-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-item .text-muted {
    font-size: 0.75rem;
}

.chapter-link {
    font-size: 0.9rem;
    color: #333;
}

.chapter-link:hover {
    color: #007bff;
}

/* Latest Releases */
.latest-releases {
    flex: 1;
    max-width: 1200px;
    margin-right: 0.9rem;
}

.chapter-release-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: #fff;
    border: 1px solid #edf2f7;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.chapter-release-item:last-child {
    margin-bottom: 0;
}

.popular-novels {
    width: 300px;
    flex-shrink: 0;
}

@media (max-width: 1200px) {
    .latest-releases {
        max-width: 100%;
        margin-right: 0;
        margin-bottom: 2rem;
    }
    
    .popular-novels {
        width: 100%;
    }
}

/* Chapters Table */
.chapters-list-header {
    padding: 1rem;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.chapters-list-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.bulk-actions {
    padding: 0.5rem 1rem;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chapters-table-wrapper {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.chapters-table {
    width: 100%;
    border-collapse: collapse;
}

.chapters-table th,
.chapters-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.chapters-table th {
    text-align: left;
    font-weight: 600;
    color: #495057;
    background: #f8f9fa;
    white-space: nowrap;
}

.chapters-table .check-column {
    width: 40px;
    text-align: center;
}

.chapters-table .novel-title-column {
    min-width: 200px;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chapters-table .chapter-title-column {
    min-width: 200px;
}

.chapters-table .chapter-title-column a {
    color: #333;
    text-decoration: none;
}

.chapters-table .chapter-title-column a:hover {
    color: #007bff;
}

.chapters-table .volume-column,
.chapters-table .chapter-number-column {
    width: 80px;
    text-align: center;
}

.chapters-table .date-column {
    width: 120px;
    color: #6c757d;
    white-space: nowrap;
}

.chapters-table tr:hover {
    background-color: #f8f9fa;
}

.chapters-table .no-chapters {
    text-align: center;
    color: #6c757d;
    padding: 2rem;
}

.chapters-table .pagination-row {
    text-align: center;
    background: #f8f9fa;
}

.chapters-table .pagination {
    display: inline-flex;
    padding: 1rem 0;
    margin: 0;
}

/* Search Box */
.search-box {
    position: relative;
}

.search-box input {
    padding-right: 30px;
    width: 200px;
}

/* Pagination */
.pagination {
    margin-top: 2rem;
    justify-content: center;
}

.pagination .page-numbers {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 4px;
    background: #fff;
    color: #333;
    text-decoration: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pagination .current {
    background: #007bff;
    color: #fff;
}

/* Novel Detail Page */
.novel-header {
    background: #fff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.novel-cover {
    position: relative;
    width: 100%;
    max-width: 250px;
    margin: 0 auto;
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
}

.novel-cover img {
    width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: block;
}

.novel-score {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.novel-score .score {
    margin-bottom: 0.5rem;
}

.rating {
    text-align: center;
}

.stars {
    display: inline-flex;
    gap: 4px;
}

.stars i {
    color: #ffc107;
    font-size: 1.25rem;
    cursor: pointer;
    transition: transform 0.2s;
}

.stars i:hover {
    transform: scale(1.2);
}

.rating-count {
    font-size: 0.85rem;
    color: #6c757d;
}

.novel-actions .btn {
    font-weight: 500;
}

.novel-actions .badge {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.4em 0.8em;
    text-decoration: none;
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
}

.novel-actions .badge:hover {
    background: #e9ecef;
}

.novel-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
}

.novel-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    font-size: 13px;
    color: #8f95a3;
}

.meta-item {
    color: #666;
}

.meta-item strong {
    color: #333;
    display: inline-block;
    margin-right: 0.5rem;
}

.novel-excerpt {
    font-style: italic;
    color: #666;
    border-left: 3px solid #007bff;
    padding-left: 1rem;
}

.novel-synopsis {
    position: relative;
    color: #4a4a4a;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 0.95rem;
}

.novel-synopsis .read-more {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    padding: 0;
    border: none;
    background: none;
}

.novel-synopsis .read-more:hover {
    text-decoration: underline;
}

.novel-synopsis .full-synopsis {
    margin-top: 1rem;
}

.read-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #666;
}

/* Chapters Section */
.chapters-section {
    background: #fff;
    padding: 2rem;
    border-radius: 4px;
    margin-top: 2rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chapters-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.volume-section {
    margin-bottom: 2rem;
}

.volume-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #495057;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.chapter-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    font-size: 13px;
    line-height: 1.4;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-link {
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
}

.chapter-link:hover {
    color: #007bff;
}

.chapter-date {
    font-size: 0.85rem;
    color: #718096;
    white-space: nowrap;
}

.chapter-time {
    font-size: 0.85rem;
    color: #718096;
    white-space: nowrap;
}

.chapter-item .chapter-title {
    font-weight: 400;
}

/* Responsive */
@media (max-width: 768px) {
    .novel-header {
        padding: 1rem;
    }

    .novel-cover {
        max-width: 200px;
        margin: 0 auto 1.5rem;
        padding-bottom: 266px; /* Fixed height for mobile */
    }

    .novel-title {
        text-align: center;
        font-size: 1.5rem;
        margin-top: 1rem;
    }

    .novel-meta {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .chapter-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .chapter-date {
        margin-left: 0;
    }
}

/* Novel Sidebar */
.novel-sidebar {
    position: sticky;
    top: 20px;
}

.novel-sidebar .card {
    margin-bottom: 1.5rem;
    border-radius: 8px;
    overflow: hidden;
}

.novel-sidebar .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.novel-sidebar .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.novel-sidebar .card-body {
    padding: 1rem;
}

.novel-genres {
    margin-top: 1rem;
}

.novel-genres .badge {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.4em 0.8em;
    margin: 0.2rem;
    background-color: #6c757d;
    color: #fff;
    text-decoration: none;
    transition: background-color 0.2s;
}

.novel-genres .badge:hover {
    background-color: #5a6268;
}

.novel-rating {
    text-align: center;
}

.novel-rating .stars {
    display: inline-block;
    cursor: pointer;
}

.novel-rating .stars i {
    color: #ffc107;
    font-size: 1.2rem;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.novel-rating .stars i:hover {
    transform: scale(1.2);
}

.novel-rating .rating-count {
    color: #666;
    font-size: 0.9rem;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    text-align: right;
}

.chapter-content {
    max-width: 1000px;
    margin: 0 auto;
    line-height: 1.8;
    background-color: #fff;
}

.chapter-title {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: #333;
}

.chapter-text {
    margin-bottom: 2rem;
}

.chapter-text p {
    margin-bottom: 1.5rem;
}

.breadcrumb {
    padding: 0;
    margin-bottom: 1rem;
    background-color: transparent;
}

.breadcrumb-item a {
    color: #1a73e8;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #666;
}

.chapter-title {
    color: #1a73e8;
    font-size: 24px;
    margin-bottom: 0.5rem;
}

.chapter-subtitle {
    color: #333;
    font-size: 20px;
    margin-bottom: 1rem;
}

.chapter-meta {
    color: #666;
    font-size: 14px;
    margin-bottom: 1.5rem;
}

.btn {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
}

.btn i {
    margin-right: 6px;
}

.btn-primary {
    background-color: #1a73e8;
    border-color: #1a73e8;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #ddd;
    color: #333;
}

.btn-facebook {
    background-color: #1877f2;
    border-color: #1877f2;
    color: white;
}

.btn-twitter {
    background-color: #1da1f2;
    border-color: #1da1f2;
    color: white;
}

.btn-whatsapp {
    background-color: #25d366;
    border-color: #25d366;
    color: white;
}

.btn-pinterest {
    background-color: #e60023;
    border-color: #e60023;
    color: white;
}

.chapter-content {
    font-family: 'Fira Sans', sans-serif;
    font-size: 16px;
    line-height: 160%;
    color: #333;
    background-color: #fff;
    padding: 2rem;
    margin-top: 1.5rem;
}

@media (max-width: 768px) {
    .btn {
        padding: 4px 8px;
        font-size: 13px;
    }
    
    .chapter-title {
        font-size: 20px;
    }
    
    .chapter-subtitle {
        font-size: 16px;
    }
    
    .chapter-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .chapter-social {
        flex-wrap: wrap;
    }
}

.card-title {
    font-family: 'Fira Sans', sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 5px;
}

small, .small {
    font-size: 12px;
    color: #8f95a3;
}

.synopsis {
    font-size: 13px;
    line-height: 1.5;
    color: #444;
}

.navbar-nav {
    font-family: 'Fira Sans', sans-serif;
    font-size: 13px;
    font-weight: 500;
}

.btn {
    font-family: 'Fira Sans', sans-serif;
    font-size: 13px;
    font-weight: 500;
    padding: 4px 8px;
}

.btn-sm {
    font-size: 12px;
    padding: 2px 6px;
}

.reading-progress {
    font-size: 12px;
    color: #8f95a3;
}

/* Novel List Items */
.novel-item h3 {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Chapter Item Styling */
.latest-releases .popular-item-chapter {
    margin-top: 0.25rem;
    width: 110%;
}

.popular-section .popular-item-chapter {
    margin-top: 0.25rem;
    width: 100%;
}

.popular-item-chapter .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width:100%
}

.popular-item-chapter a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 1rem;
    white-space: nowrap;
}

.popular-item-chapter a:hover {
    color: #3498db;
}

.popular-item-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin: 2px 0;
}

/* Rating Stars */
.rating-stars {
    display: inline-flex;
    gap: 2px;
}

.rating-stars i {
    font-size: 0.9rem;
}

.rating-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: #2c3e50;
}

.chapter-release-rating {
    margin-top: 4px;
}

.chapter-breadcrumb .breadcrumb {
    margin-bottom: 0;
    font-size: 14px;
}

.chapter-header {
    margin: 2rem 0;
}

.chapter-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #2196F3;
}

.chapter-subtitle {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 1rem;
}

.chapter-meta {
    font-size: 14px;
    color: #666;
}

.chapter-actions {
    margin: 1.5rem 0;
}

.btn {
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 14px;
}

.btn-facebook {
    background-color: #1877f2;
    color: white;
}

.btn-twitter {
    background-color: #1da1f2;
    color: white;
}

.btn-whatsapp {
    background-color: #25d366;
    color: white;
}

.btn-pinterest {
    background-color: #e60023;
    color: white;
}

.chapter-social .btn {
    padding: 0.5rem 1rem;
}

.chapter-social .btn i {
    margin-right: 0.5rem;
}

.chapter-content {
    font-family: 'Fira Sans', sans-serif;
    font-size: 16px;
    line-height: 160%;
    color: #333;
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

@media (max-width: 768px) {
    .chapter-title {
        font-size: 1.5rem;
    }
    
    .chapter-subtitle {
        font-size: 1rem;
    }
    
    .chapter-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .chapter-social {
        flex-wrap: wrap;
    }
}

/* Modal Options */
.modal-content {
    border-radius: 8px;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 500;
}

.modal-body {
    padding: 1rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-select {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.text-muted {
    font-size: 0.875rem;
}

#resetOptions {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 4px;
}

#resetOptions:hover {
    background-color: #e9ecef;
}
