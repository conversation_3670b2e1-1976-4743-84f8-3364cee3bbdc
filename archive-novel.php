<?php get_header(); ?>

<div class="container py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="mb-4">
                <h1 class="h4 mb-0">All Novels</h1>
            </div>

            <?php if (have_posts()) : ?>
                <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-3 g-4">
                    <?php while (have_posts()) : the_post(); ?>
                        <div class="col">
                            <div class="card h-100 novel-card">
                                <div class="novel-cover">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('novel-cover', ['class' => 'card-img-top']); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body p-2">
                                    <h5 class="card-title">
                                        <a href="<?php the_permalink(); ?>" class="text-decoration-none text-truncate d-block">
                                            <?php the_title(); ?>
                                        </a>
                                    </h5>
                                    <?php 
                                    $rating_data = get_novel_rating(get_the_ID());
                                    $rating = $rating_data['average'];
                                    if ($rating > 0) : ?>
                                        <div class="rating-stars small">
                                            <?php for ($i = 1; $i <= 5; $i++) : ?>
                                                <?php if ($i <= $rating) : ?>
                                                    <i class="bi bi-star-fill text-warning"></i>
                                                <?php elseif ($i - 0.5 <= $rating) : ?>
                                                    <i class="bi bi-star-half text-warning"></i>
                                                <?php else : ?>
                                                    <i class="bi bi-star text-warning"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                            <span class="rating-value ms-1"><?php echo number_format($rating, 1); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>

                <div class="pagination justify-content-center mt-4">
                    <?php
                    echo paginate_links(array(
                        'prev_text' => '&laquo; Previous',
                        'next_text' => 'Next &raquo;',
                        'type' => 'list',
                        'class' => 'pagination'
                    ));
                    ?>
                </div>

            <?php else : ?>
                <div class="alert alert-info">
                    No novels found. Please check back later.
                </div>
            <?php endif; ?>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Filter</h5>
                </div>
                <div class="card-body">
                    <form action="" method="get" class="novel-filters">
                        <!-- Sort Order -->
                        <div class="mb-3">
                            <label class="form-label">Sort By</label>
                            <select name="orderby" class="form-select form-select-sm">
                                <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>>Latest Update</option>
                                <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>>Title (A-Z)</option>
                                <option value="rating" <?php selected(get_query_var('orderby'), 'rating'); ?>>Rating (High-Low)</option>
                            </select>
                        </div>

                        <!-- Time Filter -->
                        <div class="mb-3">
                            <label class="form-label">Time Period</label>
                            <select name="time" class="form-select form-select-sm">
                                <option value="all" <?php selected(isset($_GET['time']) ? $_GET['time'] : '', 'all'); ?>>All Time</option>
                                <option value="today" <?php selected(isset($_GET['time']) ? $_GET['time'] : '', 'today'); ?>>Today</option>
                                <option value="week" <?php selected(isset($_GET['time']) ? $_GET['time'] : '', 'week'); ?>>This Week</option>
                                <option value="month" <?php selected(isset($_GET['time']) ? $_GET['time'] : '', 'month'); ?>>This Month</option>
                            </select>
                        </div>

                        <!-- Rating Filter -->
                        <div class="mb-3">
                            <label class="form-label">Minimum Rating</label>
                            <select name="rating" class="form-select form-select-sm">
                                <option value="">Any Rating</option>
                                <?php
                                for ($i = 5; $i >= 1; $i--) {
                                    printf(
                                        '<option value="%1$d" %2$s>%1$d+ Stars</option>',
                                        $i,
                                        selected(isset($_GET['rating']) ? $_GET['rating'] : '', $i, false)
                                    );
                                }
                                ?>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary btn-sm w-100">Apply Filters</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.novel-card {
    transition: transform 0.2s;
    border: 1px solid rgba(0,0,0,.125);
    background: #fff;
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.novel-cover {
    position: relative;
    padding: 8px;
    background: #f8f9fa;
}

.card-img-top {
    width: 100%;
    height: 320px;
    object-fit: contain;
    background: #f8f9fa;
}

.card-title {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
    height: 2.8em;
    overflow: hidden;
}

.card-title a {
    color: inherit;
}

.rating-stars {
    font-size: 0.8rem;
}

.rating-value {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Pagination styling */
.pagination {
    margin: 0;
}

.page-numbers {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #007bff;
    text-decoration: none;
}

.page-numbers.current {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.page-numbers:hover:not(.current) {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0056b3;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .novel-card {
        background: #2d3436;
        border-color: #444;
    }
    
    .novel-cover {
        background: #1a1a1a;
    }
    
    .card-img-top {
        background: #1a1a1a;
    }
    
    .card-title a {
        color: #f1f1f1;
    }
    
    .text-muted {
        color: #aaa !important;
    }

    .page-numbers {
        background-color: #2d3436;
        border-color: #444;
        color: #007bff;
    }

    .page-numbers.current {
        background-color: #007bff;
        border-color: #007bff;
        color: #fff;
    }

    .page-numbers:hover:not(.current) {
        background-color: #1a1a1a;
        border-color: #444;
    }
}

@media (max-width: 768px) {
    .card-img-top {
        height: 280px;
    }
}

@media (max-width: 576px) {
    .card-img-top {
        height: 240px;
    }
}
</style>

<?php get_footer(); ?>
