<?php
/**
 * Flush rewrite rules for Google OAuth
 * Run this file once after implementing OAuth to ensure callback URLs work
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

// Flush rewrite rules
flush_rewrite_rules(true);

echo "Rewrite rules flushed successfully!\n";
echo "OAuth callback URL should now work: " . home_url('/oauth/google/callback') . "\n";
echo "You can delete this file after running it.\n";
?>
