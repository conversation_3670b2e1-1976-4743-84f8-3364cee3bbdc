<?php
/*
Template Name: Login
*/

// Redirect if already logged in
if (is_user_logged_in()) {
    if (current_user_can('administrator')) {
        wp_redirect(admin_url());
    } else {
        wp_redirect(home_url('/dashboard'));
    }
    exit;
}

get_header();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="h4 mb-2">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>

                    <?php
                    // Display login errors
                    if (isset($_GET['login_error'])) {
                        echo '<div class="alert alert-danger">' . esc_html(urldecode($_GET['login_error'])) . '</div>';
                    }
                    
                    // Display logout message
                    if (isset($_GET['logged_out'])) {
                        echo '<div class="alert alert-success">You have been logged out successfully.</div>';
                    }
                    ?>

                    <form method="post" action="">
                        <?php wp_nonce_field('ln_reader_login', 'ln_reader_login_nonce'); ?>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </button>
                    </form>

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="<?php echo home_url('/reset-password'); ?>" class="text-decoration-none">
                                Forgot your password?
                            </a>
                        </p>
                        <p class="mb-0">
                            Don't have an account? 
                            <a href="<?php echo home_url('/register'); ?>" class="text-decoration-none">
                                Sign up here
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-body {
    background: #fff;
    border-radius: 10px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.alert {
    border-radius: 8px;
    border: none;
}

.text-muted {
    color: #6c757d !important;
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .col-md-6 {
        padding-left: 0;
        padding-right: 0;
    }
}
</style>

<?php get_footer(); ?>
