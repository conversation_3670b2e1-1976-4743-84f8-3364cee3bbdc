<?php
/**
 * Fix Fatal Error - Emergency script to fix the import notice error
 * Run this file to fix the fatal error and clear import warnings
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

echo "<h2>LN Reader Fatal Error Fix</h2>";

// Clear import logs and warnings to prevent the error
$deleted_log = delete_option('ln_reader_import_log');
$deleted_report = delete_option('ln_reader_import_report');

// Clear all user meta for dismissed notices
global $wpdb;
$deleted_meta = $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'ln_reader_import_notice_dismissed_%'");

echo "<p>✅ <strong>Import logs cleared:</strong> " . ($deleted_log ? 'Yes' : 'No') . "</p>";
echo "<p>✅ <strong>Import reports cleared:</strong> " . ($deleted_report ? 'Yes' : 'No') . "</p>";
echo "<p>✅ <strong>User dismissal data cleared:</strong> " . $deleted_meta . " entries</p>";

echo "<hr>";
echo "<h3>Error Analysis:</h3>";
echo "<p>The fatal error occurred because:</p>";
echo "<ul>";
echo "<li>Function <code>ln_reader_display_import_report</code> was referenced but not found</li>";
echo "<li>There was a mismatch between function name and add_action call</li>";
echo "<li>This has been fixed in the functions.php file</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>✅ Fatal error should now be resolved!</strong></p>";
echo "<p>The import warnings have been cleared and the function reference has been fixed.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Go back to your WordPress admin</li>";
echo "<li>The fatal error should be gone</li>";
echo "<li>No more import warnings should appear</li>";
echo "<li>Delete this file after confirming everything works</li>";
echo "</ol>";

echo "<hr>";
echo "<p><a href='" . admin_url() . "'>← Back to WordPress Admin</a></p>";

// Also try to flush any cached errors
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
}
?>
