/* Authentication Pages Styles */

/* General Auth Page Styles */
.auth-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    background: #fff;
}

.auth-card .card-body {
    padding: 2.5rem;
}

/* Form Styles */
.auth-form .form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.auth-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.auth-form .btn {
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-form .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.auth-form .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

/* Links */
.auth-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Header Styles */
.auth-header h2 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 10px;
}

.auth-header p {
    color: #6c757d;
    font-size: 16px;
}

/* Form Check Styles */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    color: #495057;
    font-size: 14px;
}

/* Form Text */
.form-text {
    font-size: 13px;
    color: #6c757d;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card .card-body {
        padding: 2rem 1.5rem;
    }
    
    .auth-form .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

@media (max-width: 576px) {
    .auth-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .auth-header h2 {
        font-size: 1.5rem;
    }
    
    .auth-header p {
        font-size: 14px;
    }
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Password Strength Indicator */
.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 5px;
    transition: all 0.3s ease;
}

.password-strength.weak {
    background-color: #dc3545;
    width: 33%;
}

.password-strength.medium {
    background-color: #ffc107;
    width: 66%;
}

.password-strength.strong {
    background-color: #28a745;
    width: 100%;
}

/* Social Login Buttons (if needed in future) */
.social-login {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn-social {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-google {
    background-color: #db4437;
    border-color: #db4437;
    color: white;
}

.btn-google:hover {
    background-color: #c23321;
    border-color: #c23321;
    color: white;
}

.btn-facebook {
    background-color: #3b5998;
    border-color: #3b5998;
    color: white;
}

.btn-facebook:hover {
    background-color: #2d4373;
    border-color: #2d4373;
    color: white;
}

/* Dashboard specific styles */
.dashboard-stats .card {
    transition: transform 0.3s ease;
}

.dashboard-stats .card:hover {
    transform: translateY(-5px);
}

.user-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0 auto 20px;
}

/* Tab Navigation */
.nav-tabs .nav-link {
    border-radius: 10px 10px 0 0;
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 5px;
}

.nav-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
}

.nav-tabs .nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

.nav-tabs .nav-link.active:hover {
    background-color: #007bff;
    color: white;
}

/* Reading Progress Bar */
.reading-progress {
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 10px;
}

.reading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Novel Cards in Dashboard */
.novel-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.novel-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.novel-cover {
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.novel-card:hover .novel-cover {
    transform: scale(1.05);
}
