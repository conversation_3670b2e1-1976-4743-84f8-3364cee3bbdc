<?php
/**
 * Clear Import Warnings - One-time script to remove import warnings
 * Run this file once to clear all import warnings and logs
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

// Clear import logs and warnings
delete_option('ln_reader_import_log');
delete_option('ln_reader_import_report');

// Clear all user meta for dismissed notices
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'ln_reader_import_notice_dismissed_%'");

echo "✅ Import warnings and logs cleared successfully!\n";
echo "The warning notice should no longer appear in your WordPress admin.\n";
echo "You can delete this file after running it.\n";
?>
