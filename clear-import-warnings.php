<?php
/**
 * Clear Import Warnings - One-time script to remove import warnings
 * Run this file once to clear all import warnings and logs
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. Admin privileges required.');
}

echo "<h2>LN Reader Import Warning Cleaner</h2>";

// Clear import logs and warnings
$deleted_log = delete_option('ln_reader_import_log');
$deleted_report = delete_option('ln_reader_import_report');

// Clear all user meta for dismissed notices
global $wpdb;
$deleted_meta = $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'ln_reader_import_notice_dismissed_%'");

echo "<p>✅ <strong>Import logs cleared:</strong> " . ($deleted_log ? 'Yes' : 'No') . "</p>";
echo "<p>✅ <strong>Import reports cleared:</strong> " . ($deleted_report ? 'Yes' : 'No') . "</p>";
echo "<p>✅ <strong>User dismissal data cleared:</strong> " . $deleted_meta . " entries</p>";

echo "<hr>";
echo "<p><strong>✅ All import warnings and logs have been cleared successfully!</strong></p>";
echo "<p>The warning notice should no longer appear in your WordPress admin.</p>";
echo "<p><em>You can delete this file after running it.</em></p>";

echo "<hr>";
echo "<p><a href='" . admin_url() . "'>← Back to WordPress Admin</a></p>";
?>
